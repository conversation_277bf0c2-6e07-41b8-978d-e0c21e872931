/*
  # Initial Schema Setup

  1. Tables
    - `user_settings`
      - `id` (uuid, primary key, references auth.users)
      - `theme` (text, default 'light')
      - `created_at` (timestamp)
      - `updated_at` (timestamp)
    
    - `table_configs`
      - `id` (uuid, primary key)
      - `name` (text)
      - `description` (text)
      - `api_endpoint` (text)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
*/

-- Create user_settings table
CREATE TABLE IF NOT EXISTS user_settings (
  id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  theme text DEFAULT 'light' CHECK (theme IN ('light', 'dark')),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create table_configs table
CREATE TABLE IF NOT EXISTS table_configs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  api_endpoint text NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE table_configs ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can read own settings"
  ON user_settings
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can update own settings"
  ON user_settings
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

CREATE POLICY "Users can read table configs"
  ON table_configs
  FOR SELECT
  TO authenticated
  USING (true);

-- Insert default table configurations
INSERT INTO table_configs (name, description, api_endpoint) VALUES
  ('Todo List', 'A list of todos from JSONPlaceholder', 'https://jsonplaceholder.typicode.com/todos'),
  ('BTC/USDT Trades', 'Recent Bitcoin trades from Binance', 'https://api.binance.com/api/v3/trades?symbol=BTCUSDT');
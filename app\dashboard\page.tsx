'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/lib/supabase/client';
import Link from 'next/link';

interface TableConfig {
  id: string;
  name: string;
  description: string;
  api_endpoint: string;
}

export default function DashboardPage() {
  const [tables, setTables] = useState<TableConfig[]>([]);

  useEffect(() => {
    const fetchTables = async () => {
      const { data, error } = await supabase
        .from('table_configs')
        .select('*');

      if (error) {
        console.error('Error fetching tables:', error);
        return;
      }

      setTables(data);
    };

    fetchTables();
  }, []);

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {tables.map((table) => (
        <Link key={table.id} href={`/dashboard/table/${table.id}`}>
          <Card className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader>
              <CardTitle>{table.name}</CardTitle>
              <CardDescription>{table.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground truncate">
                {table.api_endpoint}
              </p>
            </CardContent>
          </Card>
        </Link>
      ))}
    </div>
  );
}
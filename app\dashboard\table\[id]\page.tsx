'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/data-table';
import { supabase } from '@/lib/supabase/client';
import { ArrowLeft } from 'lucide-react';
import useSWR from 'swr';

interface TableConfig {
  id: string;
  name: string;
  description: string;
  api_endpoint: string;
}

const fetcher = (url: string) => fetch(url).then((res) => res.json());

export default function TablePage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [tableConfig, setTableConfig] = useState<TableConfig | null>(null);
  const { data, error, isLoading, mutate } = useSWR(
    tableConfig?.api_endpoint,
    fetcher
  );

  useEffect(() => {
    const fetchTableConfig = async () => {
      const { data, error } = await supabase
        .from('table_configs')
        .select('*')
        .eq('id', params.id)
        .single();

      if (error) {
        console.error('Error fetching table config:', error);
        return;
      }

      setTableConfig(data);
    };

    fetchTableConfig();
  }, [params.id]);

  const columns = data?.[0]
    ? Object.keys(data[0]).map((key) => ({
        accessorKey: key,
        header: key.charAt(0).toUpperCase() + key.slice(1),
      }))
    : [];

  if (!tableConfig) return null;

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push('/dashboard')}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold">{tableConfig.name}</h1>
          <p className="text-muted-foreground">{tableConfig.description}</p>
        </div>
      </div>

      {error ? (
        <div className="text-destructive">Failed to load data</div>
      ) : (
        <DataTable
          columns={columns}
          data={data || []}
          onRefresh={() => mutate()}
          isLoading={isLoading}
        />
      )}
    </div>
  );
}